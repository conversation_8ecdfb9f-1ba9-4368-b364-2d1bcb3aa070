import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/dartMain/dartMain_logic/authentication_logic.dart';
import 'package:web_app/dartMain/dartMain_screens/main_screen.dart';
import 'package:web_app/dartMain/dartMain_screens/projects_screen.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;
import 'package:web_app/readerMode/main_screens/main_screen_reader.dart';
import 'package:web_app/dartMain/dartMain_screens/account_details_screen.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/dartMain/dartMain_screens/projects_with_applications_list_screen.dart';
import 'package:web_app/readerMode/main_screens/subscriptions_screen_reader.dart';
import 'package:web_app/readerMode/main_screens/SentApplications_screen_reader.dart';
import 'package:web_app/readerMode/main_screens/marketplace_screen_reader.dart';
import 'package:web_app/readerMode/main_logic/account_details_logic.dart';

class NavDrawerPlus extends ConsumerWidget {
  final FirebaseAuth auth = FirebaseAuth.instance;
  final String currentRoute;
  final FetchDatabase fetch = FetchDatabase();
  final AuthenticationLogic authObject = AuthenticationLogic();
  final AccountDetailsLogic accountDetailsLogic = AccountDetailsLogic();

  NavDrawerPlus({super.key, required this.currentRoute});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentMode = ref.watch(modeProvider);

    return currentMode == 'Reader Mode'
        ? bookBranchPlusReaderNavDrawer(context, ref)
        : bookBranchPlusNavDrawer(context, ref);
  }

  Widget bookBranchPlusReaderNavDrawer(BuildContext context, WidgetRef ref) {
    final currentMode = ref.watch(modeProvider);

    return Drawer(
      elevation: 2.0,
      child: Column(
        children: <Widget>[
          // User Info Container
          _buildUserInfoContainer(context, ref),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              children: <Widget>[
                const Padding(
                  padding: EdgeInsets.only(left: 16.0, top: 8.0, bottom: 8.0),
                  child: Text(
                    'NAVIGATION',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
                // Navigation Items
                _buildNavItem(
                  icon: Icons.home,
                  title: 'Home',
                  context: context,
                  route: MainScreenReader.routeName,
                  isSelected: currentRoute == MainScreenReader.routeName,
                ),
                _buildNavItem(
                  icon: Icons.subscriptions,
                  title: 'Subscriptions',
                  context: context,
                  route: SubscriptionsScreenReader.routeName,
                  isSelected:
                      currentRoute == SubscriptionsScreenReader.routeName,
                ),
                _buildNavItem(
                  icon: Icons.store,
                  title: 'Marketplace',
                  context: context,
                  route: MarketPlaceScreenReader.routeName,
                  isSelected: currentRoute == MarketPlaceScreenReader.routeName,
                ),
                _buildNavItem(
                  icon: Icons.pending,
                  title: 'Pending Applications',
                  context: context,
                  route: SentApplicationsScreenReader.routeName,
                  isSelected:
                      currentRoute == SentApplicationsScreenReader.routeName,
                ),
                const Divider(height: 32, thickness: 0.5),
                _buildMoreNavItem(context: context, ref: ref, isSelected: true),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                _buildModeButton(
                  ref,
                  title: 'Creator Mode',
                  icon: Icons.create,
                  route: MainScreen.routeName,
                  context: context,
                  isSelected: currentMode == 'Creator Mode',
                ),
                const SizedBox(height: 8.0),
                _buildModeButton(
                  ref,
                  title: 'Reader Mode',
                  icon: Icons.book_online,
                  route: MainScreenReader.routeName,
                  context: context,
                  isSelected: currentMode == 'Reader Mode',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget bookBranchPlusNavDrawer(BuildContext context, WidgetRef ref) {
    final currentMode = ref.watch(modeProvider);

    return Drawer(
      elevation: 2.0,
      child: Column(
        children: <Widget>[
          // User Info Container
          _buildUserInfoContainer(context, ref),
          Expanded(
            child: ListView(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              children: <Widget>[
                const Padding(
                  padding: EdgeInsets.only(left: 16.0, top: 8.0, bottom: 8.0),
                  child: Text(
                    'NAVIGATION',
                    style: TextStyle(
                      color: Colors.grey,
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      letterSpacing: 1.2,
                    ),
                  ),
                ),
                // Navigation Items
                _buildNavItem(
                  icon: Icons.home,
                  title: 'Home',
                  context: context,
                  route: MainScreen.routeName,
                  isSelected: currentRoute == MainScreen.routeName,
                ),
                _buildNavItem(
                  icon: Icons.subscriptions,
                  title: 'Projects',
                  context: context,
                  route: ProjectsScreen.routeName,
                  isSelected: currentRoute == ProjectsScreen.routeName,
                ),
                _buildNavItem(
                  icon: Icons.pending,
                  title: 'Applicants',
                  context: context,
                  route: ProjectsWithApplicationsListScreen.routeName,
                  isSelected: currentRoute ==
                      ProjectsWithApplicationsListScreen.routeName,
                ),
                const Divider(height: 32, thickness: 0.5),
                _buildMoreNavItem(context: context, ref: ref, isSelected: true),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              children: [
                _buildModeButton(
                  ref,
                  title: 'Creator Mode',
                  icon: Icons.create,
                  route: MainScreen.routeName,
                  context: context,
                  isSelected: currentMode == 'Creator Mode',
                ),
                const SizedBox(height: 8.0),
                _buildModeButton(
                  ref,
                  title: 'Reader Mode',
                  icon: Icons.book_online,
                  route: MainScreenReader.routeName,
                  context: context,
                  isSelected: currentMode == 'Reader Mode',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfoContainer(BuildContext context, WidgetRef ref) {
    return SafeArea(
      child: Container(
        height: 120,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              globals.bookBranchGreen.withAlpha(25),
              Colors.white,
            ],
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
          child: Row(
            children: [
              InkWell(
                onTap: () {
                  Navigator.of(context)
                      .push(MaterialPageRoute(builder: (context) {
                    return AccountDetailsScreen();
                  }));
                },
                child: Container(
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withAlpha(25),
                        spreadRadius: 1,
                        blurRadius: 3,
                        offset: const Offset(0, 1),
                      ),
                    ],
                  ),
                  child: CircleAvatar(
                    radius: 28,
                    backgroundColor: globals.bookBranchGreen,
                    child: CircleAvatar(
                      radius: 26,
                      backgroundColor: Colors.white,
                      child: Text(
                        ref.watch(currentUserFirstLetterProvider),
                        style: TextStyle(
                          color: globals.bookBranchGreen,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Text(
                      ref.watch(displayNameProvider),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Colors.black87,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      FirebaseAuth.instance.currentUser?.email ?? '',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ),
              ),
              IconButton(
                icon: Icon(
                  Icons.arrow_forward_ios,
                  size: 16,
                  color: Colors.grey[600],
                ),
                onPressed: () {
                  Navigator.of(context)
                      .push(MaterialPageRoute(builder: (context) {
                    return AccountDetailsScreen();
                  }));
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildNavItem({
    required IconData icon,
    required String title,
    required BuildContext context,
    required String route,
    required bool isSelected,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: isSelected ? globals.bookBranchGreen.withAlpha(25) : null,
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? globals.bookBranchGreen : Colors.grey[700],
          size: 22,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? globals.bookBranchGreen : Colors.grey[800],
            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
          ),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        onTap: () {
          Navigator.pushNamed(context, route);
        },
      ),
    );
  }

  Widget _buildMoreNavItem({
    required BuildContext context,
    required WidgetRef ref,
    required bool isSelected,
  }) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.0),
      ),
      child: ExpansionTile(
        leading: Icon(
          Icons.more_horiz,
          color: Colors.grey[700],
          size: 22,
        ),
        title: Text(
          'More',
          style: TextStyle(
            color: Colors.grey[800],
            fontWeight: FontWeight.normal,
          ),
        ),
        iconColor: globals.bookBranchGreen,
        collapsedIconColor: Colors.grey[700],
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        children: <Widget>[
          Container(
            margin: const EdgeInsets.only(left: 16.0),
            child: ListTile(
              leading: Icon(Icons.help, color: Colors.grey[700], size: 22),
              title: Text('Help', style: TextStyle(color: Colors.grey[800])),
              onTap: () {
                Navigator.pushNamed(context, '/helpScreen');
              },
            ),
          ),
          Container(
            margin: const EdgeInsets.only(left: 16.0),
            child: ListTile(
              leading: Icon(Icons.gavel, color: Colors.grey[700], size: 22),
              title: Text('Terms of Use',
                  style: TextStyle(color: Colors.grey[800])),
              onTap: () {
                Navigator.pushNamed(context, '/legalScreen');
              },
            ),
          ),
          Container(
            margin: const EdgeInsets.only(left: 16.0),
            child: ListTile(
              leading: Icon(Icons.logout, color: Colors.grey[700], size: 22),
              title: Text('Logout', style: TextStyle(color: Colors.grey[800])),
              onTap: () {
                authObject.logoutUser(ref);
                Navigator.pushNamed(context, '/Authentication');
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModeButton(WidgetRef ref,
      {required String title,
      required IconData icon,
      required String route,
      required bool isSelected,
      required BuildContext context}) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
      decoration: BoxDecoration(
        color: isSelected ? globals.bookBranchGreen : Colors.grey[200],
        borderRadius: BorderRadius.circular(8.0),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(25),
            spreadRadius: 1,
            blurRadius: 3,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: ListTile(
        leading: Icon(
          icon,
          color: isSelected ? Colors.white : Colors.grey[700],
          size: 22,
        ),
        title: Text(
          title,
          style: TextStyle(
            color: isSelected ? Colors.white : Colors.grey[800],
            fontWeight: FontWeight.w600,
          ),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8.0),
        ),
        onTap: () {
          ref.read(modeProvider.notifier).state = title;
          Navigator.pushNamed(context, route);
        },
      ),
    );
  }
}
