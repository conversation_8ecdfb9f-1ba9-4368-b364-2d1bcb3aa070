import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:web_app/main.dart';
import 'package:web_app/widgets/universal_widgets.dart';
import 'package:web_app/database/database_logic/database_logic_fetch/fetch_database.dart';
import 'package:web_app/database/database_logic/write_database_metadata.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:web_app/readerMode/main_logic/account_details_logic.dart';
import 'package:web_app/dartMain/dartMain_logic/authentication_logic.dart';
import 'package:web_app/dartMain/dartMain_logic/fetch_main.dart';
import 'package:cloud_functions/cloud_functions.dart';
import 'package:web_app/dartMain/dartMain_logic/globals.dart' as globals;

class AccountDetailsWidget extends ConsumerStatefulWidget {
  const AccountDetailsWidget({super.key});

  @override
  AccountDetailsWidgetState createState() => AccountDetailsWidgetState();
}

class AccountDetailsWidgetState extends ConsumerState<AccountDetailsWidget> {
  final UniversalWidgets universal = UniversalWidgets();
  final FetchDatabase fetch = FetchDatabase();
  final FetchMain fetchMain = FetchMain();
  final WriteDatabase writeMetadata = WriteDatabase();
  final FirebaseAuth auth = FirebaseAuth.instance;
  final WriteDatabase writeDatabase = WriteDatabase();
  final AuthenticationLogic authObject = AuthenticationLogic();
  final AccountDetailsLogic accountDetailsLogic = AccountDetailsLogic();

  final TextEditingController _displayNameController = TextEditingController();

  // State variable for notifications status
  bool notificationsStatus = false;

  @override
  Widget build(BuildContext context) {
    final User user = auth.currentUser!;
    final uid = user.uid;
    final String accountPlan = ref.watch(planTypeProvider);
    ref.watch(notificationsEnabledProvider);

    return FutureBuilder(
      future: Future.wait([
        fetch.fetchDisplayName(),
        fetch.fetchProjectCountForDeletion(),
        fetchMain.fetchNotificationStatus(),
        fetch.fetchStorageUsedByUser(),
        fetchMain.fetchProjectIDsByOwner(uid)
      ]),
      builder: (context, AsyncSnapshot<List<dynamic>> snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(
              child: CircularProgressIndicator(
                  color: Color.fromARGB(255, 44, 148, 44)));
        } else if (snapshot.hasError) {
          return Center(child: Text('Error: ${snapshot.error}'));
        } else if (snapshot.hasData) {
          var username = snapshot.data![0]; // Data from getUsername
          var projectCount = snapshot.data![1]; // Data from getOtherData
          notificationsStatus = snapshot.data![2]; // Data from getOtherData
          double storageUsed = snapshot.data![3]; // Data from getOtherData
          List<String> projectIDs =
              snapshot.data![4]; // Data from fetchProjectIDsByOwner

          return Scaffold(
            backgroundColor: Colors.grey[50],
            body: SafeArea(
              child: SingleChildScrollView(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Profile Header Section
                    _buildProfileHeader(context, username),

                    // Main Content
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const SizedBox(height: 24),

                          // Personal Information Section
                          _buildSectionHeader('Personal Information'),
                          _buildCard(
                            context,
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildDisplayNameField(context, ref, username),
                                const SizedBox(height: 8),
                                _buildEmailField(
                                    user.email ?? 'No email available'),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Account Information Section
                          _buildSectionHeader('Account Information'),
                          _buildCard(
                            context,
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                _buildInfoRow(
                                    context,
                                    'Number of Projects',
                                    projectCount.toString(),
                                    Icons.folder_outlined),
                                const Divider(height: 24),
                                _buildInfoRow(
                                    context,
                                    'Storage Used',
                                    _formatStorageSize(storageUsed),
                                    Icons.storage_outlined),
                                const Divider(height: 24),
                                _buildInfoRow(context, 'Account Plan',
                                    accountPlan, Icons.workspace_premium,
                                    isHighlighted:
                                        accountPlan == 'BookBranch+'),
                              ],
                            ),
                          ),

                          const SizedBox(height: 24),

                          // Preferences Section
                          _buildSectionHeader('Preferences'),
                          _buildCard(
                            context,
                            _buildNotificationsToggle(context, ref),
                          ),

                          const SizedBox(height: 32),

                          // Danger Zone
                          _buildSectionHeader('Danger Zone', isWarning: true),
                          _buildDangerCard(
                            context,
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                const Text(
                                  'Delete your account',
                                  style: TextStyle(
                                    fontSize: 16,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                const Text(
                                  'This action cannot be undone. All of your data will be permanently deleted.',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.black87,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                SizedBox(
                                  width: double.infinity,
                                  child: ElevatedButton.icon(
                                    icon: const Icon(Icons.delete_forever),
                                    label: const Text('Delete Account'),
                                    onPressed: () =>
                                        showWarningDialogForAccountDeletion(
                                            context, ref, projectIDs),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: Colors.red.shade50,
                                      foregroundColor: Colors.red.shade900,
                                      elevation: 0,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                        side: BorderSide(
                                            color: Colors.red.shade300),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),

                          const SizedBox(height: 40),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        } else {
          return const Center(child: Text('No data available'));
        }
      },
    );
  }

  // Format storage size with appropriate units
  String _formatStorageSize(double storageGB) {
    if (storageGB >= 1.0) {
      return '${storageGB.toStringAsFixed(2)} GB';
    } else if (storageGB >= 0.001) {
      double storageMB = storageGB * 1024;
      return '${storageMB.toStringAsFixed(2)} MB';
    } else {
      double storageKB = storageGB * 1024 * 1024;
      return '${storageKB.toStringAsFixed(2)} KB';
    }
  }

  // Profile header with avatar and name
  Widget _buildProfileHeader(BuildContext context, String username) {
    return Container(
      width: double.infinity,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            globals.bookBranchGreen.withAlpha(50),
            Colors.white,
          ],
        ),
      ),
      padding: const EdgeInsets.all(24.0),
      child: Column(
        children: [
          Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withAlpha(20),
                  blurRadius: 10,
                  spreadRadius: 2,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: CircleAvatar(
              backgroundColor: globals.bookBranchGreen,
              radius: 50,
              child: CircleAvatar(
                backgroundColor: Colors.white,
                radius: 48,
                child: Text(
                  username.substring(0, 1).toUpperCase(),
                  style: TextStyle(
                    fontSize: 36,
                    fontWeight: FontWeight.bold,
                    color: globals.bookBranchGreen,
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            username,
            style: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ],
      ),
    );
  }

  // Section header
  Widget _buildSectionHeader(String title, {bool isWarning = false}) {
    return Padding(
      padding: const EdgeInsets.only(left: 8.0, bottom: 8.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          letterSpacing: 0.5,
          color: isWarning ? Colors.red.shade700 : Colors.grey.shade700,
        ),
      ),
    );
  }

  // Card container
  Widget _buildCard(BuildContext context, Widget child) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(10),
            blurRadius: 6,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: child,
      ),
    );
  }

  // Danger zone card
  Widget _buildDangerCard(BuildContext context, Widget child) {
    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(bottom: 8.0),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withAlpha(20),
            blurRadius: 6,
            spreadRadius: 0,
            offset: const Offset(0, 2),
          ),
        ],
        border: Border.all(
          color: Colors.red.shade100,
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: child,
      ),
    );
  }

  // Display name field with update button
  Widget _buildDisplayNameField(
      BuildContext context, WidgetRef ref, String username) {
    if (_displayNameController.text.isEmpty) {
      _displayNameController.text = username;
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Display Name',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: TextField(
                controller: _displayNameController,
                decoration: InputDecoration(
                  hintText: 'Enter your display name',
                  contentPadding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: Colors.grey.shade300),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(8),
                    borderSide: BorderSide(color: globals.bookBranchGreen),
                  ),
                  prefixIcon: const Icon(Icons.person_outline),
                ),
              ),
            ),
            const SizedBox(width: 8),
            ElevatedButton(
              onPressed: () {
                final scaffoldMessenger = ScaffoldMessenger.of(context);
                writeMetadata
                    .updateDisplayName(_displayNameController.text, ref)
                    .then((success) {
                  if (success) {
                    scaffoldMessenger.showSnackBar(
                      const SnackBar(
                        content: Text('Display name updated successfully.'),
                        backgroundColor: Color.fromARGB(255, 44, 148, 44),
                      ),
                    );
                  } else {
                    scaffoldMessenger.showSnackBar(
                      const SnackBar(
                        content: Text(
                            'Display name already exists. Please choose another name.'),
                        backgroundColor: Colors.red,
                      ),
                    );
                  }
                }).catchError((error) {
                  scaffoldMessenger.showSnackBar(
                    SnackBar(
                      content: Text('An error occurred: $error'),
                      backgroundColor: Colors.red,
                    ),
                  );
                });
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: globals.bookBranchGreen,
                foregroundColor: Colors.white,
                elevation: 0,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text('Update'),
            ),
          ],
        ),
      ],
    );
  }

  // Email field (read-only)
  Widget _buildEmailField(String email) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Email',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: Colors.grey,
          ),
        ),
        const SizedBox(height: 8),
        TextField(
          readOnly: true,
          controller: TextEditingController(text: email),
          decoration: InputDecoration(
            contentPadding:
                const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            disabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8),
              borderSide: BorderSide(color: Colors.grey.shade300),
            ),
            filled: true,
            fillColor: Colors.grey.shade50,
            prefixIcon: const Icon(Icons.email_outlined),
            suffixIcon: const Icon(Icons.lock_outline, size: 16),
          ),
        ),
      ],
    );
  }

  // Information row with icon
  Widget _buildInfoRow(
      BuildContext context, String label, String value, IconData icon,
      {bool isHighlighted = false}) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: isHighlighted
                ? globals.bookBranchGreen.withAlpha(30)
                : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color:
                isHighlighted ? globals.bookBranchGreen : Colors.grey.shade700,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                value,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight:
                      isHighlighted ? FontWeight.w600 : FontWeight.normal,
                  color:
                      isHighlighted ? globals.bookBranchGreen : Colors.black87,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // Notifications toggle
  Widget _buildNotificationsToggle(BuildContext context, WidgetRef ref) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: notificationsStatus
                ? globals.bookBranchGreen.withAlpha(30)
                : Colors.grey.shade100,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            notificationsStatus
                ? Icons.notifications_active_outlined
                : Icons.notifications_off_outlined,
            color: notificationsStatus
                ? globals.bookBranchGreen
                : Colors.grey.shade700,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Notifications',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: Colors.grey.shade700,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                notificationsStatus
                    ? 'Notifications are enabled'
                    : 'Notifications are disabled',
                style: const TextStyle(
                  fontSize: 16,
                  color: Colors.black87,
                ),
              ),
            ],
          ),
        ),
        Switch(
          value: notificationsStatus,
          onChanged: (bool value) async {
            bool status = value;
            await writeDatabase.updateUserNotifications(status);
            ref.invalidate(notificationsEnabledProvider);
          },
          activeColor: globals.bookBranchGreen,
          activeTrackColor: globals.bookBranchGreen.withAlpha(100),
        ),
      ],
    );
  }

  void showWarningDialogForAccountDeletion(
      BuildContext context, WidgetRef ref, List<String> projectIDs) {
    bool isChecked = false;
    final TextEditingController passwordController = TextEditingController();
    final User user = auth.currentUser!;
    final uid = user.uid;
    final scaffoldMessenger = ScaffoldMessenger.of(context);

    // Check if user signed in with Google (no password required)
    final userProviders = user.providerData;
    bool hasGoogleProvider =
        userProviders.any((provider) => provider.providerId == 'google.com');
    bool isGoogleOnlyUser = hasGoogleProvider && userProviders.length == 1;

    showDialog(
      context: context,
      builder: (BuildContext dialogContext) {
        return StatefulBuilder(
          builder: (context, setState) {
            return AlertDialog(
              title: Row(
                children: [
                  Icon(Icons.warning_amber_rounded, color: Colors.red.shade700),
                  const SizedBox(width: 8),
                  const Text(
                    'Delete Account',
                    style: TextStyle(
                      color: Colors.black87,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              backgroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              content: SingleChildScrollView(
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.red.shade50,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.red.shade200),
                      ),
                      child: const Text(
                        'This action cannot be undone. All your projects and data will be permanently deleted. If you have subscribed to BookBranch+, please cancel your subscription in Settings on your device.',
                        style: TextStyle(
                          color: Colors.black87,
                          fontSize: 14,
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),
                    if (!isGoogleOnlyUser) ...[
                      const Text(
                        'Enter your password to confirm:',
                        style: TextStyle(
                          color: Colors.black87,
                          fontWeight: FontWeight.w600,
                          fontSize: 14,
                        ),
                      ),
                      const SizedBox(height: 8),
                      TextField(
                        controller: passwordController,
                        decoration: InputDecoration(
                          labelText: 'Password',
                          labelStyle: TextStyle(color: Colors.grey.shade700),
                          border: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          enabledBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.grey.shade300),
                          ),
                          focusedBorder: OutlineInputBorder(
                            borderRadius: BorderRadius.circular(8),
                            borderSide: BorderSide(color: Colors.red.shade300),
                          ),
                          prefixIcon: const Icon(Icons.lock_outline),
                          contentPadding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 12),
                        ),
                        obscureText: true,
                        style: const TextStyle(color: Colors.black87),
                      ),
                    ] else ...[
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.blue.shade50,
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: Colors.blue.shade200),
                        ),
                        child: Row(
                          children: [
                            Icon(Icons.info_outline,
                                color: Colors.blue.shade700, size: 20),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'You signed in with Google. No password verification required.',
                                style: TextStyle(
                                  color: Colors.blue.shade700,
                                  fontSize: 14,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                    const SizedBox(height: 16),
                    Row(
                      children: [
                        Checkbox(
                          value: isChecked,
                          onChanged: (bool? value) {
                            setState(() {
                              isChecked = value!;
                            });
                          },
                          activeColor: Colors.red.shade700,
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(4),
                          ),
                        ),
                        Expanded(
                          child: Text(
                            'I understand this action cannot be undone',
                            style: TextStyle(
                              color: Colors.grey.shade800,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              actions: <Widget>[
                TextButton(
                  child: Text(
                    'Cancel',
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  onPressed: () {
                    Navigator.of(dialogContext).pop();
                  },
                ),
                ElevatedButton(
                  onPressed: isChecked
                      ? () async {
                          // First validate the password (only for non-Google users)
                          final String enteredPassword =
                              passwordController.text.trim();

                          if (!isGoogleOnlyUser && enteredPassword.isEmpty) {
                            scaffoldMessenger.showSnackBar(
                              const SnackBar(
                                content: Text(
                                    'Please enter your password to confirm deletion.'),
                                backgroundColor: Colors.red,
                              ),
                            );
                            return;
                          }

                          Navigator.of(dialogContext).pop(); // Close the dialog

                          // Show progress indicator for password verification
                          if (navigatorKey.currentState?.mounted ?? false) {
                            showDialog(
                              context: navigatorKey.currentContext!,
                              barrierDismissible: false,
                              builder: (BuildContext context) {
                                return Center(
                                  child: Container(
                                    padding: const EdgeInsets.all(24),
                                    decoration: BoxDecoration(
                                      color: Colors.white,
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const CircularProgressIndicator(
                                          color:
                                              Color.fromARGB(255, 44, 148, 44),
                                        ),
                                        const SizedBox(height: 16),
                                        Text(
                                          isGoogleOnlyUser
                                              ? 'Verifying account...'
                                              : 'Verifying password...',
                                          style: TextStyle(
                                            color: Colors.grey.shade800,
                                            fontWeight: FontWeight.w500,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                );
                              },
                            );
                          }

                          try {
                            // Verify password first
                            bool isPasswordValid = await authObject
                                .reauthenticateUser(enteredPassword);

                            if (!isPasswordValid) {
                              // Close progress indicator
                              if (navigatorKey.currentState?.mounted ?? false) {
                                navigatorKey.currentState?.pop();
                              }

                              scaffoldMessenger.showSnackBar(
                                const SnackBar(
                                  content: Text(
                                      'Incorrect password. Please try again.'),
                                  backgroundColor: Colors.red,
                                ),
                              );
                              return;
                            }

                            // Update progress indicator message
                            if (navigatorKey.currentState?.mounted ?? false) {
                              navigatorKey.currentState
                                  ?.pop(); // Close verification dialog
                            }

                            // Show deletion progress indicator
                            if (navigatorKey.currentState?.mounted ?? false) {
                              showDialog(
                                context: navigatorKey.currentContext!,
                                barrierDismissible: false,
                                builder: (BuildContext context) {
                                  return Center(
                                    child: Container(
                                      padding: const EdgeInsets.all(24),
                                      decoration: BoxDecoration(
                                        color: Colors.white,
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                      child: Column(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          const CircularProgressIndicator(
                                            color: Color.fromARGB(
                                                255, 44, 148, 44),
                                          ),
                                          const SizedBox(height: 16),
                                          Text(
                                            'Deleting account...',
                                            style: TextStyle(
                                              color: Colors.grey.shade800,
                                              fontWeight: FontWeight.w500,
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  );
                                },
                              );
                            }

                            // Get a reference to the callable function
                            FirebaseFunctions functions =
                                FirebaseFunctions.instance;
                            HttpsCallable callable = functions
                                .httpsCallable('deleteMultipleProjects');

                            // Call the function and pass parameters
                            await callable.call({'projectIDs': projectIDs});

                            // Call deleteUser.js
                            HttpsCallable callableUser =
                                functions.httpsCallable('deleteUser');
                            await callableUser.call({
                              'userID': uid,
                            });

                            if (navigatorKey.currentState?.mounted ?? false) {
                              navigatorKey.currentState
                                  ?.pop(); // Close the progress indicator
                              navigatorKey.currentState
                                  ?.pushNamedAndRemoveUntil(
                                      '/Authentication', (route) => false);
                            }
                          } catch (e) {
                            if (navigatorKey.currentState?.mounted ?? false) {
                              navigatorKey.currentState
                                  ?.pop(); // Close the progress indicator
                            }

                            // Use the stored scaffoldMessenger to avoid context issues
                            scaffoldMessenger.showSnackBar(SnackBar(
                              content: Text('Error: $e'),
                              backgroundColor: Colors.red,
                            ));
                          }
                        }
                      : null,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red.shade700,
                    foregroundColor: Colors.white,
                    elevation: 0,
                    disabledBackgroundColor: Colors.red.shade200,
                    disabledForegroundColor: Colors.white,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('Delete Account'),
                ),
              ],
            );
          },
        );
      },
    );
  }
}
